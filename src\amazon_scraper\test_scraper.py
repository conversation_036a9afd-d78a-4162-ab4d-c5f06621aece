#!/usr/bin/env python3
"""
Simple test script to verify the Amazon scraper works without proxy functionality.
"""

import logging
from scraper import AmazonScraper

def main():
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Create scraper instance
    scraper = AmazonScraper(logger)
    
    # Test URL - a simple Amazon search
    test_url = "https://www.amazon.com/s?k=laptop&ref=nb_sb_noss"
    
    try:
        logger.info("Testing Amazon scraper without proxy functionality...")
        
        # Scrape just 1 page to test
        products = scraper.scrape_amazon_page(test_url, max_pages=1)
        
        logger.info(f"Successfully scraped {len(products)} products!")
        
        # Display first few products
        for i, product in enumerate(products[:3]):
            logger.info(f"Product {i+1}: {product.title[:50]}... - Price: {product.price}")
            
    except Exception as e:
        logger.error(f"Scraper test failed: {str(e)}")
        return False
        
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ Scraper test passed!")
    else:
        print("❌ Scraper test failed!")
