#!/usr/bin/env python3
"""
Test script to verify proxy functionality for HTTP-only proxies.
This script tests each proxy in your list to see if they work.
"""

import requests
import time
import random
from proxy_config import PROXY_LIST, get_proxy_with_auth

def test_proxy_http_only(proxy_address, timeout=10):
    """Test a proxy with HTTP-only configuration"""
    # Use the new authentication system
    proxy = get_proxy_with_auth(proxy_address)

    results = {}

    # Test HTTP endpoint
    try:
        print(f"  Testing HTTP with {proxy_address}...")
        response = requests.get(
            'http://httpbin.org/ip',
            proxies=proxy,
            timeout=timeout,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        if response.status_code == 200:
            results['http'] = {'status': 'SUCCESS', 'ip': response.json().get('origin', 'unknown')}
            print(f"    ✓ HTTP works - IP: {results['http']['ip']}")
        else:
            results['http'] = {'status': 'FAILED', 'reason': f'HTTP {response.status_code}'}
            print(f"    ✗ HTTP failed - Status: {response.status_code}")
    except Exception as e:
        results['http'] = {'status': 'FAILED', 'reason': str(e)}
        print(f"    ✗ HTTP failed - Error: {str(e)}")

    # Test HTTPS endpoint
    try:
        print(f"  Testing HTTPS with {proxy_address}...")
        response = requests.get(
            'https://httpbin.org/ip',
            proxies=proxy,
            timeout=timeout,
            verify=False,  # Disable SSL verification for HTTP proxies
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        if response.status_code == 200:
            results['https'] = {'status': 'SUCCESS', 'ip': response.json().get('origin', 'unknown')}
            print(f"    ✓ HTTPS works - IP: {results['https']['ip']}")
        else:
            results['https'] = {'status': 'FAILED', 'reason': f'HTTP {response.status_code}'}
            print(f"    ✗ HTTPS failed - Status: {response.status_code}")
    except Exception as e:
        results['https'] = {'status': 'FAILED', 'reason': str(e)}
        print(f"    ✗ HTTPS failed - Error: {str(e)}")

    return results

def test_amazon_access(proxy_address, timeout=15):
    """Test if proxy can access Amazon"""
    # Use the new authentication system
    proxy = get_proxy_with_auth(proxy_address)

    try:
        print(f"  Testing Amazon access with {proxy_address}...")
        response = requests.get(
            'https://www.amazon.com/robots.txt',  # Simple Amazon endpoint
            proxies=proxy,
            timeout=timeout,
            verify=False,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        )
        if response.status_code == 200:
            print(f"    ✓ Amazon access works - Status: {response.status_code}")
            return True
        else:
            print(f"    ✗ Amazon access failed - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ✗ Amazon access failed - Error: {str(e)}")
        return False

def main():
    """Test all proxies in the list"""
    print("Testing proxy configuration with authentication support")
    print("=" * 60)

    # Check if authentication is configured
    from proxy_config import PROXY_USERNAME, PROXY_PASSWORD
    if not PROXY_USERNAME or not PROXY_PASSWORD:
        print("⚠️  WARNING: No proxy authentication configured!")
        print("   If you're getting '407 Proxy Authentication Required' errors:")
        print("   1. Log into your proxy scrape account")
        print("   2. Find your username/password in the dashboard")
        print("   3. Edit proxy_config.py and fill in PROXY_USERNAME and PROXY_PASSWORD")
        print("   4. Run this test again")
        print()
    else:
        print(f"✓ Using authentication: {PROXY_USERNAME}:{'*' * len(PROXY_PASSWORD)}")
        print()

    working_proxies = []
    amazon_working_proxies = []

    # Disable SSL warnings
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    for i, proxy_address in enumerate(PROXY_LIST, 1):
        print(f"\n[{i}/{len(PROXY_LIST)}] Testing proxy: {proxy_address}")
        print("-" * 40)

        # Test basic HTTP/HTTPS functionality
        results = test_proxy_http_only(proxy_address)

        # Check if at least one protocol works
        http_works = results.get('http', {}).get('status') == 'SUCCESS'
        https_works = results.get('https', {}).get('status') == 'SUCCESS'

        if http_works or https_works:
            working_proxies.append(proxy_address)
            print(f"  ✓ Proxy {proxy_address} is functional")

            # Test Amazon access for working proxies
            if test_amazon_access(proxy_address):
                amazon_working_proxies.append(proxy_address)
        else:
            print(f"  ✗ Proxy {proxy_address} is not working")

        # Small delay between tests
        time.sleep(1)

    # Summary
    print("\n" + "=" * 60)
    print("PROXY TEST SUMMARY")
    print("=" * 60)

    print(f"Total proxies tested: {len(PROXY_LIST)}")
    print(f"Working proxies: {len(working_proxies)}")
    print(f"Amazon-accessible proxies: {len(amazon_working_proxies)}")

    if working_proxies:
        print(f"\nWorking proxies:")
        for proxy in working_proxies:
            amazon_status = "✓ Amazon OK" if proxy in amazon_working_proxies else "✗ Amazon blocked"
            print(f"  {proxy} - {amazon_status}")

    if amazon_working_proxies:
        print(f"\nRecommended proxies for Amazon scraping:")
        for proxy in amazon_working_proxies[:5]:  # Show top 5
            print(f"  {proxy}")
    else:
        print(f"\n⚠️  No proxies can access Amazon successfully!")
        print(f"   This might be due to:")
        print(f"   - Amazon blocking these proxy IP ranges")
        print(f"   - Proxies requiring authentication")
        print(f"   - Proxies being HTTP-only but Amazon requiring HTTPS")

        if working_proxies:
            print(f"\n💡 Suggestions:")
            print(f"   - Try using the scraper in no-proxy mode first")
            print(f"   - Check if your proxies require username/password")
            print(f"   - Verify your proxies support HTTPS tunneling")

if __name__ == "__main__":
    main()
