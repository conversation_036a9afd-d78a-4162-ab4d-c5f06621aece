""""
    Module for scraping Amazon product pages.
"""

import logging
import time
import random
import concurrent.futures
from enum import Enum
from typing import List
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import os
from datetime import datetime
import requests
from urllib.parse import quote
import re

from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement
from seleniumwire import webdriver
from seleniumwire.request import Request
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from amazon_scraper.models import Product
from amazon_scraper.db_helper import DatabaseHelper

# Configure root logger to ERROR to suppress unnecessary logs
logging.getLogger().setLevel(logging.ERROR)

# Configure specific logger for price parsing with custom format
formatter = logging.Formatter('%(levelname)s - %(message)s')
handler = logging.StreamHandler()
handler.setFormatter(formatter)

price_logger = logging.getLogger('amazon_scraper.price_parser')
price_logger.setLevel(logging.INFO)
price_logger.addHandler(handler)

# Keep selenium loggers silent
logging.getLogger("WDM").setLevel(logging.ERROR)
logging.getLogger("seleniumwire").setLevel(logging.ERROR)
logging.getLogger("urllib3").setLevel(logging.ERROR)

class DriverInitializationError(BaseException):
    message = "Unable to initialize Chrome webdriver for scraping."

class DriverGetProductsError(BaseException):
    message = "Unable to get Amazon product data with Chrome webdriver."

class MissingProductDataError(BaseException):
    message = "Missing required data for product."

class ProductXPath(str, Enum):
    PRODUCTS = "//div[@data-component-type='s-search-result']"
    TITLE = ".//a/h2/span"
    URL = ".//a/h2"
    PRICE_FULL = ".//span[contains(@class, 'a-price')]//span[contains(@class, 'a-offscreen')]"
    IMAGE_URL = ".//img"
    NEXT_PAGE = "//a[contains(@class,'s-pagination-next')]"

class AmazonScraper:
    """Class for scraping Amazon"""

    def __init__(self, logger: logging.Logger | None = None) -> None:
            self._logger = logger if logger else logging.getLogger(__name__)
            self.db = DatabaseHelper()  # Initialize database helper

    def _get_random_user_agent(self) -> str:
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Edge/*********",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (iPad; CPU OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
        ]
        return random.choice(user_agents)

    def _add_headers_to_request(self, request: Request) -> None:
        """Intercepts selenium requests to add randomized headers"""
        headers = {
            "User-Agent": self._get_random_user_agent(),
            "Accept-Language": random.choice(["en-US,en;q=0.9", "it-IT,it;q=0.9"]),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Connection": "keep-alive",
            "Referer": "https://www.amazon.it/",
            "Host": "www.amazon.it",
            "TE": "Trailers",
        }
        for key, value in headers.items():
            request.headers[key] = value

    def _init_chrome_driver(self) -> webdriver.Chrome:
        """Initializes Chrome webdriver with optimized settings for concurrent scraping"""
        chrome_options = Options()
        chrome_options.add_argument("--headless=new")
        chrome_options.add_argument("--no-sandbox")

        # Memory optimization for multiple instances
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-software-rasterizer")

        # Set low memory usage
        chrome_options.add_argument("--memory-pressure-off")
        chrome_options.add_argument("--disk-cache-size=1")
        chrome_options.add_argument("--media-cache-size=1")
        chrome_options.add_argument("--process-per-site=1")

        # Update Selenium Wire options for concurrent connections
        seleniumwire_options = {
            'verify_ssl': False,
            'suppress_connection_errors': True,
            'connection_timeout': 30,
            'connection_keep_alive': False,
            'pool_connections': 25,
            'pool_maxsize': 25,
            'max_retries': 3
        }

        try:
            service = Service(r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe")
            driver = webdriver.Chrome(
                service=service,
                options=chrome_options,
                seleniumwire_options=seleniumwire_options
            )

            driver.request_interceptor = self._add_headers_to_request
            driver.set_page_load_timeout(30)
            driver.set_window_size(800, 600)  # Reduced window size for memory savings

            return driver
        except Exception as e:
            self._logger.error(f"Failed to initialize Chrome driver: {str(e)}")
            raise DriverInitializationError("Failed to initialize Chrome driver") from e

    def _parse_price_for_product(self, product: WebElement) -> str | None:
        """Parses price for a product element using multiple fallback strategies"""

        def format_price(price_text: str) -> str | None:
            """Helper function to format and validate price"""
            try:
                cleaned = price_text.replace("€", "").replace("$", "").replace(" ", "").replace(".", "").strip()

                price_logger.debug(f"Processing raw price: {cleaned}")

                parts = cleaned.split(",")
                if len(parts) > 1:
                    cleaned = f"{parts[0]},{parts[-1]}"
                else:
                    cleaned = parts[0]

                # Convert to float (temporarily replacing comma with dot for conversion)
                price_float = float(cleaned.replace(",", "."))
                if 0.01 <= price_float <= 100000:
                    # Format back with comma
                    return f"{price_float:.2f}".replace(".", ",")

                price_logger.warning(f"Price {price_float} outside reasonable range")
                return None

            except ValueError as e:
                price_logger.warning(f"Could not convert price text: {price_text}, Error: {str(e)}")
                return None

        # Try multiple price element locations in order of preference
        price_xpaths = [
            ".//span[contains(@class, 'a-price')]//span[contains(@class, 'a-offscreen')]",
            ".//span[contains(@class, 'a-price-whole')]",
            ".//span[@data-a-color='price']//span[contains(@class, 'a-offscreen')]",
            ".//span[contains(@class, 'a-color-price')]",
            ".//span[contains(@class, 'a-price-range')]",
            ".//span[@class='a-price-symbol']/../span[@class='a-price-whole']",
            ".//span[contains(text(), '€')]",
            ".//span[contains(text(), '$')]"
        ]

        for xpath in price_xpaths:
            try:
                price_elements = product.find_elements(By.XPATH, xpath)
                for price_element in price_elements:
                    if not price_element.is_displayed():
                        continue

                    price_text = price_element.text.strip()
                    if not price_text:
                        price_text = price_element.get_attribute("textContent").strip()

                    if price_text:
                        price_logger.debug(f"Found price text: {price_text}")
                        formatted_price = format_price(price_text)
                        if formatted_price:
                            return formatted_price

            except Exception as e:
                price_logger.debug(f"Error with xpath {xpath}: {str(e)}")
                continue

        # If no price found with standard methods, try to find any text that looks like a price
        try:
            all_text_elements = product.find_elements(By.XPATH, ".//span | .//div")
            for element in all_text_elements:
                text = element.text.strip()
                if text and any(char in text for char in ['€', '$', ',']):
                    # Check if it looks like a price (contains currency symbol and numbers)
                    if re.search(r'[€$]\s*\d+[,.]?\d*', text) or re.search(r'\d+[,.]\d+\s*[€$]', text):
                        price_logger.info(f"Found potential price text: {text}")
                        formatted_price = format_price(text)
                        if formatted_price:
                            return formatted_price
        except Exception as e:
            price_logger.debug(f"Error in fallback price search: {str(e)}")

        price_logger.debug("No valid price found")
        return None

    def _parse_product_data(self, product: WebElement) -> Product:
        title = None
        for xpath in [".//a/h2/span", ".//h2//span", ".//a[@class='a-link-normal']/span"]:
            elems = product.find_elements(By.XPATH, xpath)
            if elems:
                title = elems[0].text
                break

        url = None
        for xpath in [".//a/h2", ".//a[contains(@class, 'a-link-normal')]"]:
            elems = product.find_elements(By.XPATH, xpath)
            if elems:
                url = elems[0].get_attribute("href")
                if url:
                    break

        asin_code = product.get_attribute("data-asin")

        image_url = None
        for xpath in [".//img[@class='s-image']", ".//img"]:
            elems = product.find_elements(By.XPATH, xpath)
            if elems:
                image_url = elems[0].get_attribute("src")
                if image_url:
                    break

        price = self._parse_price_for_product(product)
        if not price:
            self._logger.warning(f"Price not found for product {title[:35]}. Skipping product.")
            raise MissingProductDataError("Product missing price")

        if not all([title, url, asin_code]):
            self._logger.debug(f"Missing essential data: title={title}, url={url}, asin={asin_code}")
            raise MissingProductDataError

        return Product(title=title, url=url, asin_code=asin_code, price=price, image_url=image_url)

    def _get_products_from_page(self, url: str, driver: webdriver.Chrome) -> List[Product]:
        """Scrapes the Amazon page for products using parallel processing"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Add page load strategy
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                driver.get(url)

                time.sleep(random.uniform(4, 6))

                wait = WebDriverWait(driver, 45)  # Increased timeout to 45 seconds

                # First check if page loaded at all
                wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

                # Check for captcha
                captcha = driver.find_elements(By.XPATH, "//form[contains(@action, 'validateCaptcha')]")
                if captcha:
                    self._logger.warning("Captcha detected! Waiting longer...")
                    time.sleep(20)  # Increased wait time for captcha
                    retry_count += 1
                    continue

                # Try multiple selectors for products with better error handling
                product_found = False
                for selector in [
                    "//div[contains(@class, 's-result-item')]",
                ]:
                    try:
                        self._logger.debug(f"Trying selector: {selector}")
                        wait.until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        product_found = True
                        break
                    except Exception as e:
                        self._logger.debug(f"Selector failed: {selector}, Error: {str(e)}")
                        continue

                if not product_found:
                    raise NoSuchElementException("No products found with any selector")

                # Additional wait for page to stabilize
                time.sleep(2)

                # Get products with multiple selector attempts
                product_elements = []
                for selector in [ProductXPath.PRODUCTS, "//div[contains(@class, 's-result-item')]"]:
                    product_elements = driver.find_elements(By.XPATH, selector)
                    if product_elements:
                        break

                if not product_elements:
                    raise NoSuchElementException("No products found on page")

                # Use ThreadPoolExecutor for parallel processing with reduced workers
                parsed_products = []
                with ThreadPoolExecutor(max_workers=2) as executor:
                    future_to_product = {
                        executor.submit(self._parse_product_data, product): product
                        for product in product_elements
                    }

                    for future in concurrent.futures.as_completed(future_to_product):
                        try:
                            parsed_product = future.result()
                            parsed_products.append(parsed_product)
                        except MissingProductDataError:
                            continue
                        except Exception as e:
                            self._logger.error(f"Error parsing product: {e}")
                            continue

                if parsed_products:
                    return parsed_products
                else:
                    raise NoSuchElementException("No valid products could be parsed")

            except Exception as e:
                self._logger.warning(f"Attempt {retry_count + 1} failed: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    time.sleep(random.uniform(3, 7))  # Increased delay between retries
                    continue
                raise

        return []  # Return empty list if all retries failed

    def scrape_amazon_page(self, url: str, csv_path: str = None, max_pages: int = 10) -> List[Product]:
        """
        Retrieves a list of products from Amazon and compares with existing prices.

        Args:
            url (str): The starting Amazon page URL
            csv_path (str): Path to CSV file with existing prices
            max_pages (int): Maximum number of pages to scrape (default: 4)

        Returns:
            List[Product]: A list of Product objects with price comparison data
        """
        self._logger.info(f"Scraping Amazon product data (up to {max_pages} pages)...")

        # Read existing prices if CSV path is provided
        existing_prices = {}
        if csv_path:
            existing_prices = self._read_existing_prices(csv_path)

        try:
            driver = self._init_chrome_driver()
        except Exception as e:
            raise DriverInitializationError from e

        all_products = []
        current_url = url
        current_page = 1

        try:
            while current_url and current_page <= max_pages:
                self._logger.info(f"Scraping page {current_page}...")
                products = self._get_products_from_page(current_url, driver)

                # Compare prices and add price change information
                for product in products:
                    self._check_price_drop(product)

                all_products.extend(products)
                self._logger.info(f"Found {len(products)} products on page {current_page}")

                # Clear request queue before loading next page
                del driver.requests

                current_url = self._has_next_page(driver)
                if not current_url:
                    self._logger.info("No more pages available")
                    break

                current_page += 1
                time.sleep(random.uniform(5, 8))  # Increased delay between pages

            self._logger.info(f"Total products scraped: {len(all_products)}")
            return all_products

        except Exception as e:
            raise DriverGetProductsError from e
        finally:
            driver.close()

    def _has_next_page(self, driver: webdriver.Chrome) -> str | None:
        """Check if there's a next page and return its URL"""
        try:
            # Try multiple selectors for next page
            for xpath in [
                "//a[contains(@class,'s-pagination-next')]",
                "//a[contains(@class,'s-pagination-item') and contains(@href,'page=')]",
                "//span[@class='s-pagination-strip']/a[contains(@href,'page=')]"
            ]:
                elements = driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and "disabled" not in element.get_attribute("class"):
                        href = element.get_attribute("href")
                        if href and "page=" in href:
                            self._logger.debug(f"Found next page URL: {href}")
                            return href

            self._logger.debug("No next page found")
            return None
        except Exception as e:
            self._logger.warning(f"Error checking for next page: {str(e)}")
            return None

    def _read_existing_prices(self, csv_path: str) -> dict:
        """
        Read existing prices from CSV file.

        Args:
            csv_path (str): Path to the CSV file

        Returns:
            dict: Dictionary with ASIN codes as keys and prices as values
        """
        if not os.path.exists(csv_path):
            return {}

        try:
            df = pd.read_csv(csv_path)
            prices = {}

            for _, row in df.iterrows():
                if pd.notna(row['price']) and pd.notna(row['asin_code']):
                    # Use the price exactly as it appears in the CSV
                    prices[row['asin_code']] = row['price']

            return prices

        except Exception as e:
            self._logger.error(f"Error reading CSV file: {e}")
            return {}

    def _send_discord_alert(self, product: Product) -> None:
        """
        Sends a Discord webhook notification for significant price drops.

        Args:
            product (Product): Product with price change information
        """
        WEBHOOK_URL = "https://discord.com/api/webhooks/1364925973933457490/LZ0pgxZkJcjPVI0taYi-jitwxsSj6oYI3i2yNgUlvD6BVuGOrri8J4IH9A3t-TCnX9nS"

        # Create Discord embed
        embed = {
            "title": "🔥 Significant Price Drop Alert! 🔥",
            "color": 0x00ff00,  # Green color
            "fields": [
                {
                    "name": "Product",
                    "value": product.title[:1024],  # Discord has 1024 char limit
                    "inline": False
                },
                {
                    "name": "Old Price",
                    "value": f"${product.old_price:.2f}",
                    "inline": True
                },
                {
                    "name": "New Price",
                    "value": f"${float(product.price):.2f}",
                    "inline": True
                },
                {
                    "name": "Price Drop",
                    "value": f"{product.price_change_pct:.1f}%",
                    "inline": True
                }
            ],
            "url": product.url,
            "thumbnail": {"url": product.image_url} if product.image_url else None
        }

        # Prepare webhook data
        payload = {
            "embeds": [embed],
            "username": "Amazon Price Alert",
            "avatar_url": "https://cdn-icons-png.flaticon.com/512/732/732241.png"
        }

        try:
            response = requests.post(WEBHOOK_URL, json=payload)
            response.raise_for_status()
            self._logger.info(f"Discord notification sent for product: {product.asin_code}")
        except Exception as e:
            self._logger.error(f"Failed to send Discord notification: {str(e)}")

    def extract_price(self, product: Product) -> float:
        """
        Extract price from product and convert to float for calculations.

        Args:
            product (Product): Product object with price string

        Returns:
            float: Price as float value
        """
        try:
            # Handle price format: "123,45" -> 123.45
            price_str = product.price.replace("€", "").replace("$", "").strip()
            # Replace comma with dot for float conversion
            price_float = float(price_str.replace(",", "."))
            return price_float
        except (ValueError, AttributeError) as e:
            self._logger.warning(f"Could not extract price from {product.price}: {str(e)}")
            return 0.0

    def _check_price_drop(self, product: Product) -> None:
        """
        Check if product had a significant price drop (>40%) compared to previous price.
        Updates product with price change information and sends Discord alert if needed.

        Args:
            product (Product): Product object to check for price drops
        """
        try:
            # Extract current price as float
            current_price = self.extract_price(product)
            if current_price <= 0:
                self._logger.warning(f"Invalid current price for product {product.asin_code}")
                return

            # Get price history from database
            price_history = self.db.get_price_history(product.asin_code)

            if price_history is None:
                # No previous price data - this is a new product
                self._logger.info(f"New product detected: {product.asin_code}")
                self.db.save_product(product)
                return

            last_price, _, _ = price_history

            # Calculate price difference and percentage change
            price_diff = current_price - last_price
            price_change_pct = ((current_price - last_price) / last_price) * 100

            # Update product with price change information
            product.old_price = last_price
            product.price_diff = price_diff
            product.price_change_pct = price_change_pct

            self._logger.debug(
                f"Price change for {product.asin_code}: "
                f"Old={last_price:.2f}, New={current_price:.2f}, "
                f"Diff={price_diff:.2f}, Pct={price_change_pct:.1f}%"
            )
            # Check for significant price drop (more than 40% decrease)
            if price_change_pct <= -40.0:
                self._logger.info(
                    f"Significant price drop detected for {product.title[:35]}: "
                    f"{last_price:.2f} -> {current_price:.2f} ({price_change_pct:.1f}%)"
                )
                # Send Discord alert
                self._send_discord_alert(product)


            # Save current price to database
            self.db.save_product(product)

        except Exception as e:
            self._logger.error(f"Error checking price drop for {product.title[:35]}: {str(e)}")

    def scrape_multiple_categories(self, category_urls: List[str], csv_path: str = None, max_pages: int = 10) -> List[Product]:
        self._logger.info(f"Scraping {len(category_urls)} categories concurrently...")

        all_products = []

        # Create a thread pool to handle multiple categories
        with ThreadPoolExecutor(max_workers=min(len(category_urls), 3)) as executor:
            # Submit scraping tasks for each category
            future_to_url = {
                executor.submit(self.scrape_amazon_page, url, csv_path, max_pages): url
                for url in category_urls
            }

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    category_products = future.result()
                    self._logger.info(f"Category {url} returned {len(category_products)} products")
                    all_products.extend(category_products)
                except Exception as e:
                    self._logger.error(f"Category {url} failed with error: {str(e)}")

        self._logger.info(f"Total products scraped across all categories: {len(all_products)}")
        return all_products