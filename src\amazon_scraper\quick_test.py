#!/usr/bin/env python3
"""
Quick test script to try the scraper with different configurations.
Use this to quickly test what works best for your situation.
"""

import logging
from scraper import AmazonScraper

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_configuration(config_name: str, use_proxy: bool, advanced_proxy: bool, max_pages: int = 1):
    """Test a specific scraper configuration"""
    print(f"\n{'='*50}")
    print(f"TESTING: {config_name}")
    print(f"{'='*50}")
    print(f"Proxy: {use_proxy}, Advanced: {advanced_proxy}, Pages: {max_pages}")
    
    try:
        scraper = AmazonScraper(
            use_proxy=use_proxy,
            advanced_proxy=advanced_proxy,
            http_only_proxies=True
        )
        
        url = "https://www.amazon.it/s?k=macbook"
        products = scraper.scrape_amazon_page(url, max_pages=max_pages)
        
        if products:
            print(f"✅ SUCCESS: Found {len(products)} products")
            print(f"Sample: {products[0].title[:50]}... - {products[0].price}")
            return True
        else:
            print("❌ No products found")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        return False

def main():
    """Test different configurations quickly"""
    setup_logging()
    
    print("🔧 QUICK CONFIGURATION TESTER")
    print("Testing different scraper configurations to find what works...")
    
    # Test configurations in order of likelihood to work
    configs = [
        ("Advanced Proxy (Recommended for blocking)", True, True),
        ("Fast Proxy", True, False),
        ("No Proxy (Direct)", False, False),
    ]
    
    for config_name, use_proxy, advanced_proxy in configs:
        success = test_configuration(config_name, use_proxy, advanced_proxy)
        if success:
            print(f"\n🎉 FOUND WORKING CONFIGURATION: {config_name}")
            print(f"Use these settings in your main script:")
            print(f"  use_proxy={use_proxy}")
            print(f"  advanced_proxy={advanced_proxy}")
            print(f"  http_only_proxies=True")
            break
    else:
        print("\n😞 No configuration worked immediately")
        print("Try the bypass_blocking.py script for more advanced strategies")

if __name__ == "__main__":
    main()
