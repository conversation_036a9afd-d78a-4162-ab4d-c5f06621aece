#!/usr/bin/env python3
"""
<PERSON><PERSON>t to help bypass Amazon blocking with various strategies.
This script tries different approaches to get around the "Ci dispiace" page.
"""

import logging
import time
import random
from scraper import AmazonScraper

def setup_logging():
    """Setup logging to see what's happening"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def try_advanced_proxy_mode(url: str):
    """Try with advanced proxy mode (selenium-wire)"""
    print("\n" + "="*60)
    print("STRATEGY 1: ADVANCED PROXY MODE (SELENIUM-WIRE)")
    print("="*60)
    
    try:
        scraper = AmazonScraper(
            use_proxy=True,
            advanced_proxy=True,  # Use selenium-wire for better proxy handling
            http_only_proxies=True
        )
        
        print("Trying advanced proxy mode with selenium-wire...")
        products = scraper.scrape_amazon_page(url, max_pages=1)
        
        if products:
            print(f"✅ SUCCESS: Found {len(products)} products with advanced proxy mode")
            return True, products
        else:
            print("❌ No products found with advanced proxy mode")
            return False, []
            
    except Exception as e:
        print(f"❌ Advanced proxy mode failed: {str(e)}")
        return False, []

def try_with_longer_delays(url: str):
    """Try with much longer delays between requests"""
    print("\n" + "="*60)
    print("STRATEGY 2: LONGER DELAYS")
    print("="*60)
    
    try:
        scraper = AmazonScraper(
            use_proxy=True,
            advanced_proxy=False,
            http_only_proxies=True
        )
        
        # Monkey patch to add longer delays
        original_get_products = scraper._get_products_from_page
        
        def delayed_get_products(url, driver):
            print(f"Adding 5-10 second delay before scraping...")
            time.sleep(random.uniform(5, 10))
            return original_get_products(url, driver)
        
        scraper._get_products_from_page = delayed_get_products
        
        print("Trying with longer delays (5-10 seconds)...")
        products = scraper.scrape_amazon_page(url, max_pages=1)
        
        if products:
            print(f"✅ SUCCESS: Found {len(products)} products with longer delays")
            return True, products
        else:
            print("❌ No products found with longer delays")
            return False, []
            
    except Exception as e:
        print(f"❌ Longer delays failed: {str(e)}")
        return False, []

def try_no_proxy_mode(url: str):
    """Try without any proxy"""
    print("\n" + "="*60)
    print("STRATEGY 3: NO PROXY MODE")
    print("="*60)
    
    try:
        scraper = AmazonScraper(
            use_proxy=False,  # No proxy
            advanced_proxy=False,
            http_only_proxies=True
        )
        
        print("Trying without proxy (direct connection)...")
        products = scraper.scrape_amazon_page(url, max_pages=1)
        
        if products:
            print(f"✅ SUCCESS: Found {len(products)} products without proxy")
            return True, products
        else:
            print("❌ No products found without proxy")
            return False, []
            
    except Exception as e:
        print(f"❌ No proxy mode failed: {str(e)}")
        return False, []

def try_different_search_terms(base_url: str):
    """Try different search terms that might be less monitored"""
    print("\n" + "="*60)
    print("STRATEGY 4: DIFFERENT SEARCH TERMS")
    print("="*60)
    
    # Try different search terms
    alternative_searches = [
        "laptop apple",
        "notebook mac",
        "computer portatile",
        "macbook pro"
    ]
    
    for search_term in alternative_searches:
        try:
            # Construct URL with different search term
            test_url = f"https://www.amazon.it/s?k={search_term.replace(' ', '+')}"
            print(f"\nTrying search term: '{search_term}'")
            print(f"URL: {test_url}")
            
            scraper = AmazonScraper(
                use_proxy=True,
                advanced_proxy=False,
                http_only_proxies=True
            )
            
            products = scraper.scrape_amazon_page(test_url, max_pages=1)
            
            if products:
                print(f"✅ SUCCESS: Found {len(products)} products with search term '{search_term}'")
                return True, products
            else:
                print(f"❌ No products found with search term '{search_term}'")
                
        except Exception as e:
            print(f"❌ Search term '{search_term}' failed: {str(e)}")
            continue
    
    return False, []

def main():
    """Try different strategies to bypass Amazon blocking"""
    setup_logging()
    
    print("🚀 AMAZON BLOCKING BYPASS STRATEGIES")
    print("This script will try different approaches to get around the 'Ci dispiace' page")
    
    # Original URL that was causing issues
    original_url = "https://www.amazon.it/s?k=macbook"
    print(f"\nOriginal URL: {original_url}")
    
    strategies = [
        ("Advanced Proxy Mode", lambda: try_advanced_proxy_mode(original_url)),
        ("Longer Delays", lambda: try_with_longer_delays(original_url)),
        ("No Proxy Mode", lambda: try_no_proxy_mode(original_url)),
        ("Different Search Terms", lambda: try_different_search_terms(original_url))
    ]
    
    for strategy_name, strategy_func in strategies:
        try:
            success, products = strategy_func()
            if success:
                print(f"\n🎉 BREAKTHROUGH! {strategy_name} worked!")
                print(f"Found {len(products)} products")
                if products:
                    print("\nSample products:")
                    for i, product in enumerate(products[:3]):
                        print(f"  {i+1}. {product.title[:50]}... - {product.price}")
                
                print(f"\n✅ RECOMMENDATION: Use {strategy_name} for future scraping")
                break
            else:
                print(f"❌ {strategy_name} didn't work, trying next strategy...")
                
        except Exception as e:
            print(f"❌ {strategy_name} crashed: {str(e)}")
            continue
    else:
        print("\n😞 ALL STRATEGIES FAILED")
        print("\nAdditional suggestions:")
        print("1. Wait a few hours and try again (Amazon may have temporarily blocked your IP)")
        print("2. Try using a VPN to change your IP address")
        print("3. Use a different internet connection")
        print("4. Try scraping during off-peak hours (early morning/late night)")
        print("5. Consider using residential proxies instead of datacenter proxies")

if __name__ == "__main__":
    main()
