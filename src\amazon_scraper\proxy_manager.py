import requests
from typing import Dict, List
import random
import time
from .proxy_config import PROXY_LIST

class ProxyManager:
    def __init__(self):
        self.proxies = PROXY_LIST
        self.last_rotation = 0
        self.rotation_interval = 300  # 5 minutes

    def load_proxies(self) -> None:
        """Load proxies from config"""
        self.proxies = PROXY_LIST

    def get_proxy(self) -> Dict:
        """Get a working proxy"""
        if time.time() - self.last_rotation > self.rotation_interval:
            self.load_proxies()
            self.last_rotation = time.time()

        working_proxy = None
        while self.proxies and not working_proxy:
            proxy = random.choice(self.proxies)
            if self._test_proxy(proxy):
                working_proxy = proxy
            else:
                self.proxies.remove(proxy)

        if not working_proxy:
            raise Exception("No working proxies available")

        return working_proxy

    def _test_proxy(self, proxy: str) -> bool:
        """Test if proxy is working"""
        try:
            formatted_proxy = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
            response = requests.get(
                'https://www.amazon.it',
                proxies=formatted_proxy,
                timeout=10
            )
            return response.status_code == 200
        except:
            return False