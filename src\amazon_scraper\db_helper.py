import sqlite3
from datetime import datetime
from typing import Optional, Tuple
from contextlib import contextmanager

class DatabaseHelper:
    def __init__(self, db_path: str = "amazon_prices.db"):
        self.db_path = db_path
        self._init_db()

    @contextmanager
    def get_connection(self):
        conn = sqlite3.connect(self.db_path)
        try:
            yield conn
        finally:
            conn.close()

    def _init_db(self):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    asin_code TEXT PRIMARY KEY,
                    title TEXT,
                    url TEXT
                )
            """)

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    asin_code TEXT,
                    price REAL,
                    timestamp DATETIME,
                    FOREIGN KEY (asin_code) REFERENCES products (asin_code)
                )
            """)
            conn.commit()

    def save_product(self, product) -> None:
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO products (asin_code, title, url)
                VALUES (?, ?, ?)
            """, (product.asin_code, product.title, product.url))

            # Convert price string to float, handling comma decimal separators
            try:
                price_str = product.price.replace("€", "").replace("$", "").strip()
                price_float = float(price_str.replace(",", "."))
            except (ValueError, AttributeError):
                price_float = 0.0

            cursor.execute("""
                INSERT INTO price_history (asin_code, price, timestamp)
                VALUES (?, ?, ?)
            """, (product.asin_code, price_float, datetime.now()))
            conn.commit()

    def get_price_history(self, asin_code: str) -> Optional[Tuple[float, float, float]]:
        """Returns (last_price, current_min_price, all_time_min_price) for an ASIN"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT price, timestamp FROM price_history
                WHERE asin_code = ?
                ORDER BY timestamp DESC
                LIMIT 2
            """, (asin_code,))
            results = cursor.fetchall()

            if not results:
                return None

            current_price = results[0][0]
            last_price = results[1][0] if len(results) > 1 else current_price

            # Get all-time minimum price
            cursor.execute("""
                SELECT MIN(price) FROM price_history
                WHERE asin_code = ?
            """, (asin_code,))
            all_time_min = cursor.fetchone()[0]

            return last_price, current_price, all_time_min