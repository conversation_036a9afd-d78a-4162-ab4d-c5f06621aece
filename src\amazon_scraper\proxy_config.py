"""Configuration file for proxy settings"""

# ============================================================================
# PROXY AUTHENTICATION CONFIGURATION
# ============================================================================
# If your proxies require authentication, fill in these details:

# Option 1: Same username/password for all proxies (most common for proxy scrape)
PROXY_USERNAME = ""  # Your proxy scrape username
PROXY_PASSWORD = ""  # Your proxy scrape password

# Option 2: Individual proxy credentials (if each proxy has different auth)
# Format: {"ip:port": {"username": "user", "password": "pass"}}
INDIVIDUAL_PROXY_AUTH = {}

# ============================================================================
# PROXY LIST
# ============================================================================
# Basic proxy list (IP:PORT format)
PROXY_LIST = [
    "**************:3129",
    "*************:3129",
    "*************:3129",
    "***************:3129",
    "***************:3129",
    "***************:3129",
    "**************:3129",
    "**************:3129",
    "***************:3129",
    "***************:3129",
    "***************:3129",
    "***************:3129",
    "**************:3129",
    "**************:3129",
    "**************:3129",
    "***************:3129",
    "***************:3129",
    "***************:3129",
    "***************:3129",
    "*************:3129",
    "**************:3129",
    "***************:3129",
    "***************:3129",
    "**************:3129",
    "***************:3129"
]

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================
def get_proxy_with_auth(proxy_address):
    """
    Get proxy configuration with authentication if available

    Args:
        proxy_address (str): Proxy in format "ip:port"

    Returns:
        dict: Proxy configuration for requests library
    """
    # Check if individual auth is available for this proxy
    if proxy_address in INDIVIDUAL_PROXY_AUTH:
        auth = INDIVIDUAL_PROXY_AUTH[proxy_address]
        proxy_url = f"http://{auth['username']}:{auth['password']}@{proxy_address}"
    # Use global auth if available
    elif PROXY_USERNAME and PROXY_PASSWORD:
        proxy_url = f"http://{PROXY_USERNAME}:{PROXY_PASSWORD}@{proxy_address}"
    # No auth
    else:
        proxy_url = f"http://{proxy_address}"

    return {
        "http": proxy_url,
        "https": proxy_url
    }

def get_authenticated_proxy_list():
    """
    Get list of all proxies with authentication applied

    Returns:
        list: List of proxy configurations ready for requests
    """
    return [get_proxy_with_auth(proxy) for proxy in PROXY_LIST]